import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/models/character_type.dart';
import 'package:novel_app/models/character_card.dart';

void main() {
  group('角色多选功能测试', () {
    late NovelController controller;
    late CharacterType testType;
    late CharacterCard testCard1;
    late CharacterCard testCard2;

    setUp(() {
      Get.testMode = true;
      controller = NovelController();
      
      testType = CharacterType(
        id: 'test_type_1',
        name: '主角',
        description: '测试主角类型',
        color: 'FF0000',
      );
      
      testCard1 = CharacterCard(
        id: 'card_1',
        name: '张三',
        characterTypeId: testType.id,
        gender: '男',
        age: '25',
        personalityTraits: '勇敢、正直',
        background: '普通农民出身',
      );
      
      testCard2 = CharacterCard(
        id: 'card_2',
        name: '李四',
        characterTypeId: testType.id,
        gender: '女',
        age: '23',
        personalityTraits: '聪明、机智',
        background: '书香门第',
      );
    });

    tearDown(() {
      Get.reset();
    });

    test('应该能够为一个角色类型添加多个角色卡片', () {
      // 选择角色类型
      controller.toggleCharacterType(testType);
      expect(controller.selectedCharacterTypes.contains(testType), true);
      
      // 添加第一个角色卡片
      controller.addCharacterCard(testType.id, testCard1);
      expect(controller.getCharacterCards(testType.id).length, 1);
      expect(controller.getCharacterCards(testType.id).first.name, '张三');
      
      // 添加第二个角色卡片
      controller.addCharacterCard(testType.id, testCard2);
      expect(controller.getCharacterCards(testType.id).length, 2);
      
      final cards = controller.getCharacterCards(testType.id);
      expect(cards.any((card) => card.name == '张三'), true);
      expect(cards.any((card) => card.name == '李四'), true);
    });

    test('应该能够移除特定的角色卡片', () {
      controller.toggleCharacterType(testType);
      controller.addCharacterCard(testType.id, testCard1);
      controller.addCharacterCard(testType.id, testCard2);
      
      // 移除第一个角色卡片
      controller.removeCharacterCard(testType.id, testCard1);
      
      final cards = controller.getCharacterCards(testType.id);
      expect(cards.length, 1);
      expect(cards.first.name, '李四');
    });

    test('应该能够生成正确的角色设定字符串', () {
      controller.toggleCharacterType(testType);
      controller.addCharacterCard(testType.id, testCard1);
      controller.addCharacterCard(testType.id, testCard2);
      
      final settings = controller.getCharacterSettings();
      
      expect(settings.contains('主角设定：'), true);
      expect(settings.contains('张三'), true);
      expect(settings.contains('李四'), true);
      expect(settings.contains('勇敢、正直'), true);
      expect(settings.contains('聪明、机智'), true);
    });

    test('兼容性测试：setCharacterCard方法应该仍然有效', () {
      controller.toggleCharacterType(testType);
      controller.setCharacterCard(testType.id, testCard1);
      
      final cards = controller.getCharacterCards(testType.id);
      expect(cards.length, 1);
      expect(cards.first.name, '张三');
      
      // getFirstCharacterCard应该返回第一个角色
      final firstCard = controller.getFirstCharacterCard(testType.id);
      expect(firstCard?.name, '张三');
    });

    test('移除角色类型时应该清除所有相关角色卡片', () {
      controller.toggleCharacterType(testType);
      controller.addCharacterCard(testType.id, testCard1);
      controller.addCharacterCard(testType.id, testCard2);
      
      // 再次切换应该移除角色类型和所有角色卡片
      controller.toggleCharacterType(testType);
      
      expect(controller.selectedCharacterTypes.contains(testType), false);
      expect(controller.getCharacterCards(testType.id).length, 0);
    });

    test('不应该添加重复的角色卡片', () {
      controller.toggleCharacterType(testType);
      controller.addCharacterCard(testType.id, testCard1);
      controller.addCharacterCard(testType.id, testCard1); // 尝试添加相同的卡片
      
      expect(controller.getCharacterCards(testType.id).length, 1);
    });
  });
}
