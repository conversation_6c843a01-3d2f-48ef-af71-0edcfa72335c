import 'package:flutter_test/flutter_test.dart';
import 'package:novel_app/models/agent_message.dart';
import 'package:novel_app/models/agent_session.dart';

void main() {
  group('Agent Models Tests', () {
    test('AgentMessage应该能够正确创建用户消息', () {
      final message = AgentMessage.user(
        content: '你好，岱宗AI',
        sessionId: 'test_session',
        novelTitle: '测试小说',
        chapterNumber: 1,
      );

      expect(message.content, equals('你好，岱宗AI'));
      expect(message.type, equals(AgentMessageType.user));
      expect(message.sessionId, equals('test_session'));
      expect(message.novelTitle, equals('测试小说'));
      expect(message.chapterNumber, equals(1));
      expect(message.id, isNotEmpty);
      expect(message.timestamp, isNotNull);
    });

    test('AgentMessage应该能够正确创建Agent消息', () {
      final message = AgentMessage.agent(
        content: '你好！我是岱宗AI辅助助手。',
        sessionId: 'test_session',
        novelTitle: '测试小说',
        editSuggestionIds: ['edit1', 'edit2'],
      );

      expect(message.content, equals('你好！我是岱宗AI辅助助手。'));
      expect(message.type, equals(AgentMessageType.agent));
      expect(message.sessionId, equals('test_session'));
      expect(message.novelTitle, equals('测试小说'));
      expect(message.editSuggestionIds, equals(['edit1', 'edit2']));
    });

    test('AgentMessage应该能够正确创建系统消息', () {
      final message = AgentMessage.system(
        content: '系统消息',
        sessionId: 'test_session',
        novelTitle: '测试小说',
      );

      expect(message.content, equals('系统消息'));
      expect(message.type, equals(AgentMessageType.system));
      expect(message.sessionId, equals('test_session'));
      expect(message.novelTitle, equals('测试小说'));
    });

    test('AgentMessage应该能够正确创建错误消息', () {
      final message = AgentMessage.error(
        content: '错误消息',
        sessionId: 'test_session',
        novelTitle: '测试小说',
      );

      expect(message.content, equals('错误消息'));
      expect(message.type, equals(AgentMessageType.error));
      expect(message.sessionId, equals('test_session'));
      expect(message.novelTitle, equals('测试小说'));
    });

    test('AgentMessage应该能够转换为Map和从Map创建', () {
      final originalMessage = AgentMessage.user(
        content: '测试消息',
        sessionId: 'test_session',
        novelTitle: '测试小说',
        chapterNumber: 1,
      );

      // 转换为Map
      final map = originalMessage.toMap();
      
      // 从Map创建新消息
      final newMessage = AgentMessage.fromMap(map);

      expect(newMessage.id, equals(originalMessage.id));
      expect(newMessage.content, equals(originalMessage.content));
      expect(newMessage.type, equals(originalMessage.type));
      expect(newMessage.sessionId, equals(originalMessage.sessionId));
      expect(newMessage.novelTitle, equals(originalMessage.novelTitle));
      expect(newMessage.chapterNumber, equals(originalMessage.chapterNumber));
      expect(newMessage.timestamp.millisecondsSinceEpoch, 
             equals(originalMessage.timestamp.millisecondsSinceEpoch));
    });

    test('AgentSession应该能够正确创建聊天会话', () {
      final session = AgentSession.createChat(
        novelTitle: '测试小说',
        chapterNumber: 1,
      );

      expect(session.novelTitle, equals('测试小说'));
      expect(session.type, equals(AgentSessionType.chat));
      expect(session.chapterNumber, equals(1));
      expect(session.title, contains('测试小说'));
      expect(session.title, contains('第1章'));
      expect(session.title, contains('聊天'));
      expect(session.summary, equals('与岱宗AI辅助助手的对话'));
      expect(session.isActive, isFalse);
      expect(session.id, isNotEmpty);
      expect(session.createdAt, isNotNull);
      expect(session.lastUpdatedAt, isNotNull);
    });

    test('AgentSession应该能够正确创建创作会话', () {
      final session = AgentSession.createCreative(
        novelTitle: '测试小说',
        chapterNumber: 2,
      );

      expect(session.novelTitle, equals('测试小说'));
      expect(session.type, equals(AgentSessionType.creative));
      expect(session.chapterNumber, equals(2));
      expect(session.title, contains('测试小说'));
      expect(session.title, contains('第2章'));
      expect(session.title, contains('创作'));
      expect(session.summary, equals('与岱宗AI辅助助手的创作协作'));
    });

    test('AgentSession应该能够正确显示标题和图标', () {
      final chatSession = AgentSession.createChat(
        novelTitle: '测试小说',
        chapterNumber: 1,
      );

      final creativeSession = AgentSession.createCreative(
        novelTitle: '测试小说',
        chapterNumber: 2,
      );

      expect(chatSession.displayTitle, equals('第1章 - 聊天'));
      expect(chatSession.typeIcon, equals('💬'));

      expect(creativeSession.displayTitle, equals('第2章 - 创作'));
      expect(creativeSession.typeIcon, equals('✍️'));
    });

    test('AgentSession应该能够更新状态', () {
      final session = AgentSession.createChat(
        novelTitle: '测试小说',
      );

      final originalTime = session.lastUpdatedAt;
      
      // 等待一毫秒确保时间不同
      Future.delayed(Duration(milliseconds: 1), () {
        session.setActive(true);
        expect(session.isActive, isTrue);
        expect(session.lastUpdatedAt.isAfter(originalTime), isTrue);

        session.updateSummary('新的摘要');
        expect(session.summary, equals('新的摘要'));
      });
    });

    test('AgentSession应该能够转换为Map和从Map创建', () {
      final originalSession = AgentSession.createChat(
        novelTitle: '测试小说',
        chapterNumber: 1,
      );

      // 转换为Map
      final map = originalSession.toMap();
      
      // 从Map创建新会话
      final newSession = AgentSession.fromMap(map);

      expect(newSession.id, equals(originalSession.id));
      expect(newSession.title, equals(originalSession.title));
      expect(newSession.novelTitle, equals(originalSession.novelTitle));
      expect(newSession.type, equals(originalSession.type));
      expect(newSession.chapterNumber, equals(originalSession.chapterNumber));
      expect(newSession.summary, equals(originalSession.summary));
      expect(newSession.isActive, equals(originalSession.isActive));
      expect(newSession.createdAt.millisecondsSinceEpoch, 
             equals(originalSession.createdAt.millisecondsSinceEpoch));
      expect(newSession.lastUpdatedAt.millisecondsSinceEpoch, 
             equals(originalSession.lastUpdatedAt.millisecondsSinceEpoch));
    });
  });
}
