import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import '../controllers/novel_agent_controller.dart';
import '../models/agent_message.dart';
import '../services/novel_agent_service.dart';

/// 小说 Agent 消息气泡
/// 显示不同类型的消息，包括编辑建议
class NovelAgentMessageBubble extends StatelessWidget {
  final AgentMessage message;
  final Function(EditSuggestion)? onApplyEdit;
  final Function(EditSuggestion)? onRejectEdit;

  const NovelAgentMessageBubble({
    super.key,
    required this.message,
    this.onApplyEdit,
    this.onRejectEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: _getAlignment(),
        children: [
          _buildMessageHeader(context),
          const SizedBox(height: 4),
          _buildMessageContent(context),
          if (message.editSuggestionIds?.isNotEmpty == true)
            _buildEditSuggestions(context),
        ],
      ),
    );
  }

  /// 获取对齐方式
  CrossAxisAlignment _getAlignment() {
    switch (message.type) {
      case AgentMessageType.user:
        return CrossAxisAlignment.end;
      case AgentMessageType.agent:
      case AgentMessageType.system:
      case AgentMessageType.error:
        return CrossAxisAlignment.start;
    }
  }

  /// 构建消息头部
  Widget _buildMessageHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: message.type == AgentMessageType.user
          ? MainAxisAlignment.end
          : MainAxisAlignment.start,
      children: [
        if (message.type != AgentMessageType.user) _buildAvatar(),
        const SizedBox(width: 8),
        Text(
          _getMessageTypeText(),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          _formatTime(message.timestamp),
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[500],
          ),
        ),
        if (message.type == AgentMessageType.user) ...[
          const SizedBox(width: 8),
          _buildUserAvatar(),
        ],
      ],
    );
  }

  /// 构建头像
  Widget _buildAvatar() {
    IconData icon;
    Color color;
    
    switch (message.type) {
      case AgentMessageType.agent:
        icon = Icons.auto_fix_high;
        color = Colors.blue;
        break;
      case AgentMessageType.system:
        icon = Icons.info_outline;
        color = Colors.green;
        break;
      case AgentMessageType.error:
        icon = Icons.error_outline;
        color = Colors.red;
        break;
      case AgentMessageType.user:
        icon = Icons.person;
        color = Colors.grey;
        break;
    }
    
    return CircleAvatar(
      radius: 12,
      backgroundColor: color.withOpacity(0.1),
      child: Icon(
        icon,
        size: 14,
        color: color,
      ),
    );
  }

  /// 构建用户头像
  Widget _buildUserAvatar() {
    return CircleAvatar(
      radius: 12,
      backgroundColor: Colors.grey.withOpacity(0.1),
      child: const Icon(
        Icons.person,
        size: 14,
        color: Colors.grey,
      ),
    );
  }

  /// 构建消息内容
  Widget _buildMessageContent(BuildContext context) {
    return Container(
      constraints: const BoxConstraints(maxWidth: 300),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getBackgroundColor(context),
        borderRadius: BorderRadius.circular(12),
        border: message.type == AgentMessageType.error
            ? Border.all(color: Colors.red.withOpacity(0.3))
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMessageText(context),
          if (message.type == AgentMessageType.agent) _buildActionButtons(context),
        ],
      ),
    );
  }

  /// 构建消息文本
  Widget _buildMessageText(BuildContext context) {
    // 检查是否包含markdown语法
    if (_containsMarkdown(message.content)) {
      return _buildMarkdownContent(context);
    } else {
      return SelectableText(
        message.content,
        style: TextStyle(
          fontSize: 14,
          color: _getTextColor(context),
          height: 1.4,
        ),
      );
    }
  }

  /// 检查文本是否包含markdown语法
  bool _containsMarkdown(String text) {
    // 检查常见的markdown语法
    final markdownPatterns = [
      RegExp(r'\*\*.*?\*\*'), // 粗体
      RegExp(r'\*.*?\*'), // 斜体
      RegExp(r'`.*?`'), // 行内代码
      RegExp(r'```[\s\S]*?```'), // 代码块
      RegExp(r'^#{1,6}\s'), // 标题
      RegExp(r'^\s*[-*+]\s'), // 列表
      RegExp(r'^\s*\d+\.\s'), // 有序列表
      RegExp(r'\[.*?\]\(.*?\)'), // 链接
      RegExp(r'^>\s'), // 引用
    ];

    return markdownPatterns.any((pattern) => pattern.hasMatch(text));
  }

  /// 构建markdown内容
  Widget _buildMarkdownContent(BuildContext context) {
    return MarkdownBody(
      data: message.content,
      selectable: true,
      styleSheet: MarkdownStyleSheet(
        p: TextStyle(
          fontSize: 14,
          color: _getTextColor(context),
          height: 1.4,
        ),
        h1: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: _getTextColor(context),
        ),
        h2: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: _getTextColor(context),
        ),
        h3: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: _getTextColor(context),
        ),
        strong: TextStyle(
          fontWeight: FontWeight.bold,
          color: _getTextColor(context),
        ),
        em: TextStyle(
          fontStyle: FontStyle.italic,
          color: _getTextColor(context),
        ),
        code: TextStyle(
          fontSize: 13,
          fontFamily: 'monospace',
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[800]
              : Colors.grey[200],
          color: _getTextColor(context),
        ),
        codeblockDecoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[850]
              : Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.grey[700]!
                : Colors.grey[300]!,
          ),
        ),
        blockquote: TextStyle(
          fontSize: 14,
          fontStyle: FontStyle.italic,
          color: _getTextColor(context).withOpacity(0.8),
        ),
        blockquoteDecoration: BoxDecoration(
          border: Border(
            left: BorderSide(
              color: Theme.of(context).primaryColor,
              width: 4,
            ),
          ),
        ),
        listBullet: TextStyle(
          color: _getTextColor(context),
        ),
      ),
      onTapLink: (text, href, title) {
        // 处理链接点击
        if (href != null) {
          _handleLinkTap(href);
        }
      },
    );
  }

  /// 处理链接点击
  void _handleLinkTap(String url) {
    // 这里可以添加链接处理逻辑
    // 例如打开浏览器或显示对话框
    print('点击链接: $url');
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: const Icon(Icons.copy, size: 16),
            onPressed: () => _copyToClipboard(context),
            tooltip: '复制',
            constraints: const BoxConstraints(
              minWidth: 32,
              minHeight: 32,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.thumb_up_outlined, size: 16),
            onPressed: () => _showFeedback(context, true),
            tooltip: '有用',
            constraints: const BoxConstraints(
              minWidth: 32,
              minHeight: 32,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.thumb_down_outlined, size: 16),
            onPressed: () => _showFeedback(context, false),
            tooltip: '无用',
            constraints: const BoxConstraints(
              minWidth: 32,
              minHeight: 32,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建编辑建议
  Widget _buildEditSuggestions(BuildContext context) {
    final suggestionIds = message.editSuggestionIds!;
    
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '编辑建议 (${suggestionIds.length})',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.orange[700],
            ),
          ),
          const SizedBox(height: 8),
          ...suggestionIds.map((id) => _buildEditSuggestionItem(context, id)),
        ],
      ),
    );
  }

  /// 构建单个编辑建议项
  Widget _buildEditSuggestionItem(BuildContext context, String suggestionId) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.05),
        border: Border.all(color: Colors.orange.withOpacity(0.2)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '编辑建议 #$suggestionId',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 13,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              ElevatedButton.icon(
                onPressed: () => _applyEditSuggestion(suggestionId),
                icon: const Icon(Icons.check, size: 16),
                label: const Text('应用'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(80, 32),
                ),
              ),
              const SizedBox(width: 8),
              OutlinedButton.icon(
                onPressed: () => _rejectEditSuggestion(suggestionId),
                icon: const Icon(Icons.close, size: 16),
                label: const Text('拒绝'),
                style: OutlinedButton.styleFrom(
                  minimumSize: const Size(80, 32),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }



  /// 获取背景颜色
  Color _getBackgroundColor(BuildContext context) {
    switch (message.type) {
      case AgentMessageType.user:
        return Theme.of(context).primaryColor.withOpacity(0.1);
      case AgentMessageType.agent:
        return Colors.blue.withOpacity(0.05);
      case AgentMessageType.system:
        return Colors.green.withOpacity(0.05);
      case AgentMessageType.error:
        return Colors.red.withOpacity(0.05);
    }
  }

  /// 获取文本颜色
  Color _getTextColor(BuildContext context) {
    switch (message.type) {
      case AgentMessageType.error:
        return Colors.red[700]!;
      default:
        return Theme.of(context).textTheme.bodyMedium?.color ?? Colors.black;
    }
  }

  /// 获取消息类型文本
  String _getMessageTypeText() {
    switch (message.type) {
      case AgentMessageType.user:
        return '您';
      case AgentMessageType.agent:
        return '岱宗AI';
      case AgentMessageType.system:
        return '系统';
      case AgentMessageType.error:
        return '错误';
    }
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// 复制到剪贴板
  void _copyToClipboard(BuildContext context) {
    Clipboard.setData(ClipboardData(text: message.content));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已复制到剪贴板'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  /// 显示反馈
  void _showFeedback(BuildContext context, bool isPositive) {
    final text = isPositive ? '感谢您的反馈！' : '我们会继续改进';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(text),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  /// 应用编辑建议
  void _applyEditSuggestion(String suggestionId) {
    // TODO: 实现编辑建议应用逻辑
    print('应用编辑建议: $suggestionId');
  }

  /// 拒绝编辑建议
  void _rejectEditSuggestion(String suggestionId) {
    // TODO: 实现编辑建议拒绝逻辑
    print('拒绝编辑建议: $suggestionId');
  }
}
