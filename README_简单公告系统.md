# 🎯 简单公告系统使用指南

## 📋 概述
这是一个极简的公告系统，您只需要编辑文本文件就能更新应用中的公告。

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install fastapi uvicorn
```

### 2. 启动服务器
```bash
python simple_announcement_server.py
```

### 3. 编辑公告
编辑 `announcement.txt` 文件：
```
这是公告标题
这是公告的详细内容。
您可以写多行内容。
支持换行和格式。
```

### 4. 配置公告属性
编辑 `announcement_config.json` 文件：
```json
{
  "is_important": true,
  "is_active": true
}
```

## 📁 文件说明

### `announcement.txt` - 公告内容文件
- **第一行**：公告标题
- **其余行**：公告内容
- **编码**：UTF-8
- **修改后立即生效**

### `announcement_config.json` - 公告配置文件
```json
{
  "is_important": false,  // 是否为重要公告（红色显示）
  "is_active": true       // 是否激活公告
}
```

## 🌐 API 端点

### 获取最新公告
```
GET http://your-server.com:8000/api/v1/announcements/latest
```

### 查看服务状态
```
GET http://your-server.com:8000/status
```

## 📝 使用示例

### 发布普通公告
1. 编辑 `announcement.txt`：
```
版本更新通知
应用已更新到 v4.2.6，新增了多项功能改进。
请及时更新以获得最佳体验。
```

2. 编辑 `announcement_config.json`：
```json
{
  "is_important": false,
  "is_active": true
}
```

### 发布重要公告
1. 编辑 `announcement.txt`：
```
紧急维护通知
服务器将于今晚 22:00-24:00 进行维护。
维护期间可能影响部分功能使用。
```

2. 编辑 `announcement_config.json`：
```json
{
  "is_important": true,
  "is_active": true
}
```

### 关闭公告
编辑 `announcement_config.json`：
```json
{
  "is_important": false,
  "is_active": false
}
```

## 🔧 部署到服务器

### 方法1：直接运行
```bash
# 上传文件到服务器
scp simple_announcement_server.py user@your-server:/path/to/app/

# SSH到服务器
ssh user@your-server

# 安装依赖
pip install fastapi uvicorn

# 启动服务（后台运行）
nohup python simple_announcement_server.py > announcement.log 2>&1 &
```

### 方法2：使用systemd服务
创建 `/etc/systemd/system/announcement.service`：
```ini
[Unit]
Description=Simple Announcement Service
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/your/app
ExecStart=/usr/bin/python3 simple_announcement_server.py
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl enable announcement
sudo systemctl start announcement
sudo systemctl status announcement
```

### 方法3：使用Docker
创建 `Dockerfile`：
```dockerfile
FROM python:3.9-slim

WORKDIR /app

RUN pip install fastapi uvicorn

COPY simple_announcement_server.py .

EXPOSE 8000

CMD ["python", "simple_announcement_server.py"]
```

运行：
```bash
docker build -t simple-announcement .
docker run -d -p 8000:8000 -v $(pwd):/app simple-announcement
```

## 📱 客户端配置

修改客户端的API配置，将公告API地址指向您的服务器：
```
http://your-server.com:8000/api/v1/announcements/latest
```

## 🔍 测试验证

### 测试API
```bash
# 测试服务器状态
curl http://your-server.com:8000/status

# 测试获取公告
curl http://your-server.com:8000/api/v1/announcements/latest
```

### 测试客户端
1. 在应用中点击侧边栏的"刷新公告"
2. 查看是否显示最新公告

## 💡 使用技巧

### 1. 快速编辑
```bash
# 使用vim编辑公告
vim announcement.txt

# 使用nano编辑公告
nano announcement.txt
```

### 2. 批量操作
```bash
# 一键发布公告
echo "新版本发布\n请更新到最新版本获得更好体验" > announcement.txt

# 一键关闭公告
echo '{"is_important": false, "is_active": false}' > announcement_config.json
```

### 3. 定时公告
使用cron定时发布公告：
```bash
# 编辑crontab
crontab -e

# 每天9点发布公告
0 9 * * * echo "每日提醒\n记得备份您的重要作品" > /path/to/announcement.txt
```

## 🛠️ 故障排除

### 问题1：客户端无法获取公告
- 检查服务器是否运行：`curl http://your-server.com:8000/status`
- 检查防火墙端口8000是否开放
- 检查文件权限是否正确

### 问题2：公告内容乱码
- 确保文件使用UTF-8编码保存
- 检查服务器locale设置

### 问题3：修改后不生效
- 文件修改后立即生效，无需重启服务
- 检查文件是否保存成功
- 查看服务器日志：`tail -f announcement.log`

## 📞 支持

如有问题，请检查：
1. 服务器状态页面：`http://your-server.com:8000/status`
2. 服务器日志文件
3. 文件权限和编码
