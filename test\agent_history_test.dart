import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:novel_app/models/agent_message.dart';
import 'package:novel_app/models/agent_session.dart';
import 'package:novel_app/services/agent_history_service.dart';

void main() {
  group('Agent History Service Tests', () {
    late AgentHistoryService service;

    setUpAll(() async {
      // 初始化Hive（内存模式）
      Hive.init('test');
      
      // 注册适配器
      if (!Hive.isAdapterRegistered(12)) {
        Hive.registerAdapter(AgentMessageTypeAdapter());
      }
      if (!Hive.isAdapterRegistered(13)) {
        Hive.registerAdapter(AgentMessageAdapter());
      }
      if (!Hive.isAdapterRegistered(14)) {
        Hive.registerAdapter(AgentSessionTypeAdapter());
      }
      if (!Hive.isAdapterRegistered(15)) {
        Hive.registerAdapter(AgentSessionAdapter());
      }
    });

    setUp(() async {
      // 清理之前的测试数据
      try {
        if (Hive.isBoxOpen('agent_messages')) {
          await Hive.box('agent_messages').close();
        }
      } catch (e) {
        // 忽略关闭错误
      }

      try {
        if (Hive.isBoxOpen('agent_sessions')) {
          await Hive.box('agent_sessions').close();
        }
      } catch (e) {
        // 忽略关闭错误
      }

      // 创建服务实例
      Get.reset();
      service = Get.put(AgentHistoryService());
      await service.init();
    });

    tearDown(() async {
      // 清理测试数据
      Get.reset();
      try {
        if (Hive.isBoxOpen('agent_messages')) {
          await Hive.box('agent_messages').clear();
          await Hive.box('agent_messages').close();
        }
      } catch (e) {
        // 忽略清理错误
      }

      try {
        if (Hive.isBoxOpen('agent_sessions')) {
          await Hive.box('agent_sessions').clear();
          await Hive.box('agent_sessions').close();
        }
      } catch (e) {
        // 忽略清理错误
      }
    });

    test('应该能够创建新的聊天会话', () async {
      // 创建聊天会话
      final session = await service.createSession(
        novelTitle: '测试小说',
        type: AgentSessionType.chat,
      );

      expect(session.novelTitle, equals('测试小说'));
      expect(session.type, equals(AgentSessionType.chat));
      expect(session.isActive, isTrue);
      expect(service.sessions.length, equals(1));
      expect(service.currentSession.value, equals(session));
    });

    test('应该能够创建新的创作会话', () async {
      // 创建创作会话
      final session = await service.createSession(
        novelTitle: '测试小说',
        type: AgentSessionType.creative,
        chapterNumber: 1,
      );

      expect(session.novelTitle, equals('测试小说'));
      expect(session.type, equals(AgentSessionType.creative));
      expect(session.chapterNumber, equals(1));
      expect(session.isActive, isTrue);
      expect(service.sessions.length, equals(1));
    });

    test('应该能够添加和检索消息', () async {
      // 创建会话
      final session = await service.createSession(
        novelTitle: '测试小说',
        type: AgentSessionType.chat,
      );

      // 添加用户消息
      final userMessage = AgentMessage.user(
        content: '你好，岱宗AI',
        sessionId: session.id,
        novelTitle: '测试小说',
      );
      await service.addMessage(userMessage);

      // 添加Agent消息
      final agentMessage = AgentMessage.agent(
        content: '你好！我是岱宗AI辅助助手。',
        sessionId: session.id,
        novelTitle: '测试小说',
      );
      await service.addMessage(agentMessage);

      // 验证消息
      expect(service.messages.length, equals(2));
      expect(service.messages[0].content, equals('你好，岱宗AI'));
      expect(service.messages[0].type, equals(AgentMessageType.user));
      expect(service.messages[1].content, equals('你好！我是岱宗AI辅助助手。'));
      expect(service.messages[1].type, equals(AgentMessageType.agent));
    });

    test('应该能够切换会话', () async {
      // 创建第一个会话
      final session1 = await service.createSession(
        novelTitle: '测试小说1',
        type: AgentSessionType.chat,
      );
      
      // 添加消息到第一个会话
      await service.addMessage(AgentMessage.user(
        content: '第一个会话的消息',
        sessionId: session1.id,
        novelTitle: '测试小说1',
      ));

      // 创建第二个会话
      final session2 = await service.createSession(
        novelTitle: '测试小说2',
        type: AgentSessionType.creative,
      );

      // 添加消息到第二个会话
      await service.addMessage(AgentMessage.user(
        content: '第二个会话的消息',
        sessionId: session2.id,
        novelTitle: '测试小说2',
      ));

      // 验证当前是第二个会话
      expect(service.currentSession.value, equals(session2));
      expect(service.messages.length, equals(1));
      expect(service.messages[0].content, equals('第二个会话的消息'));

      // 切换回第一个会话
      await service.loadSessionHistory(session1.id);

      // 验证切换成功
      expect(service.currentSession.value, equals(session1));
      expect(service.messages.length, equals(1));
      expect(service.messages[0].content, equals('第一个会话的消息'));
    });

    test('应该能够删除会话', () async {
      // 创建会话
      final session = await service.createSession(
        novelTitle: '测试小说',
        type: AgentSessionType.chat,
      );

      // 添加消息
      await service.addMessage(AgentMessage.user(
        content: '测试消息',
        sessionId: session.id,
        novelTitle: '测试小说',
      ));

      // 验证会话和消息存在
      expect(service.sessions.length, equals(1));
      expect(service.messages.length, equals(1));

      // 删除会话
      await service.deleteSession(session.id);

      // 验证会话和消息都被删除
      expect(service.sessions.length, equals(0));
      expect(service.messages.length, equals(0));
      expect(service.currentSession.value, isNull);
    });

    test('应该能够按小说标题过滤会话', () async {
      // 创建不同小说的会话
      await service.createSession(
        novelTitle: '小说A',
        type: AgentSessionType.chat,
      );
      
      await service.createSession(
        novelTitle: '小说B',
        type: AgentSessionType.creative,
      );
      
      await service.createSession(
        novelTitle: '小说A',
        type: AgentSessionType.creative,
      );

      // 验证总会话数
      expect(service.sessions.length, equals(3));

      // 验证按小说过滤
      final novelASessions = service.getSessionsForNovel('小说A');
      expect(novelASessions.length, equals(2));
      
      final novelBSessions = service.getSessionsForNovel('小说B');
      expect(novelBSessions.length, equals(1));
    });
  });
}
