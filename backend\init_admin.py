#!/usr/bin/env python3
"""
初始化管理员账户脚本
"""
import os
import sys
from sqlalchemy.orm import Session
from passlib.context import CryptContext

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import engine, get_db
from app.models import User, Base

# 创建密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_admin_user():
    """创建管理员用户"""
    # 创建数据库表
    Base.metadata.create_all(bind=engine)
    
    # 获取数据库会话
    db = next(get_db())
    
    try:
        # 检查是否已存在管理员用户
        admin_user = db.query(User).filter(User.username == "admin").first()
        
        if admin_user:
            print("管理员用户已存在")
            return
        
        # 创建管理员用户
        admin_password = os.getenv("ADMIN_PASSWORD", "admin123")
        hashed_password = pwd_context.hash(admin_password)
        
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=hashed_password,
            is_active=True,
            is_admin=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print(f"管理员用户创建成功！")
        print(f"用户名: admin")
        print(f"密码: {admin_password}")
        print(f"请及时修改默认密码！")
        
    except Exception as e:
        print(f"创建管理员用户失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_admin_user()
