import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/smart_composer_controller.dart';
import '../../utils/theme_utils.dart';
import '../../controllers/api_config_controller.dart';
import '../../models/smart_composer_models.dart';

/// 快速AI设置界面
/// 用于快速配置AI模型，直接使用现有的API配置
class QuickAISetupScreen extends StatefulWidget {
  const QuickAISetupScreen({super.key});

  @override
  State<QuickAISetupScreen> createState() => _QuickAISetupScreenState();
}

class _QuickAISetupScreenState extends State<QuickAISetupScreen> {
  final SmartComposerController _smartComposerController = Get.find<SmartComposerController>();
  final ApiConfigController _apiConfig = Get.find<ApiConfigController>();

  @override
  void initState() {
    super.initState();
    // 初始化时自动同步配置
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _smartComposerController.syncFromApiConfig();
      if (mounted) setState(() {});
    });
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI模型配置'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('完成'),
          ),
        ],
      ),
      body: Obx(() => SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatusCard(),
            const SizedBox(height: 16),
            _buildAvailableModelsCard(),
            const SizedBox(height: 16),
            _buildQuickActionsCard(),
          ],
        ),
      )),
    );
  }

  Widget _buildStatusCard() {
    final availableProviders = _smartComposerController.availableProviders
        .where((p) => _smartComposerController.isProviderConfigured(p.id))
        .toList();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  availableProviders.isNotEmpty ? Icons.check_circle : Icons.warning,
                  color: availableProviders.isNotEmpty ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  availableProviders.isNotEmpty ? 'AI模型已配置' : '需要配置AI模型',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (availableProviders.isNotEmpty) ...[
              Text('已配置 ${availableProviders.length} 个AI提供商'),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: availableProviders.map((provider) {
                  return Chip(
                    label: Text(provider.type.displayName),
                    backgroundColor: Colors.green.withOpacity(0.1),
                  );
                }).toList(),
              ),
            ] else ...[
              const Text('请先在设置中配置API密钥，然后点击"同步配置"按钮。'),
              const SizedBox(height: 12),
              ElevatedButton(
                onPressed: () => Get.toNamed('/settings'),
                child: const Text('去设置'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAvailableModelsCard() {
    final availableModels = _smartComposerController.availableModels
        .where((m) => _smartComposerController.isProviderConfigured(m.providerId))
        .toList();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '可用模型',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (availableModels.isEmpty) ...[
              const Text('暂无可用模型'),
            ] else ...[
              DropdownButtonFormField<String>(
                value: _smartComposerController.settings.value.defaultChatModelId,
                decoration: const InputDecoration(
                  labelText: '默认模型',
                  border: OutlineInputBorder(),
                ),
                dropdownColor: ThemeUtils.dropdownBackgroundColor,
                style: ThemeUtils.dropdownTextStyle,
                items: availableModels.map((model) {
                  return ThemeUtils.createDropdownMenuItem<String>(
                    value: model.id,
                    text: '${model.id} (${model.providerType.displayName})',
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    _smartComposerController.setDefaultModel(value);
                  }
                },
              ),
              const SizedBox(height: 12),
              Text('共 ${availableModels.length} 个可用模型'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '快速操作',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: () async {
                    await _smartComposerController.syncFromApiConfig();
                    setState(() {}); // 刷新界面
                  },
                  icon: const Icon(Icons.sync),
                  label: const Text('同步配置'),
                ),

                ElevatedButton.icon(
                  onPressed: _testConnection,
                  icon: const Icon(Icons.wifi_tethering),
                  label: const Text('测试连接'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _testConnection() async {
    final defaultModel = _smartComposerController.defaultModel;
    if (defaultModel == null) {
      ThemeUtils.showErrorSnackbar('错误', '请先选择默认模型');
      return;
    }

    try {
      ThemeUtils.showLoadingDialog(
        title: '正在测试连接...',
        message: '请稍候，正在验证AI模型连接',
      );

      await _smartComposerController.sendMessage(
        content: '你好，请回复"连接成功"',
      );

      Get.back(); // 关闭加载对话框
      ThemeUtils.showSuccessSnackbar('成功', 'AI模型连接正常');
    } catch (e) {
      Get.back(); // 关闭加载对话框
      ThemeUtils.showErrorSnackbar('错误', '连接失败：$e');
    }
  }
}
