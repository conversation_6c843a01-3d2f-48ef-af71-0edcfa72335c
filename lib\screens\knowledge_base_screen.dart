import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/knowledge_base_controller.dart';
import 'package:novel_app/models/knowledge_document.dart';
import 'package:novel_app/widgets/themed_dropdown.dart';

class KnowledgeBaseScreen extends StatelessWidget {
  final KnowledgeBaseController controller =
      Get.find<KnowledgeBaseController>();
  final TextEditingController searchController = TextEditingController();
  final RxString searchQuery = ''.obs;
  final RxString selectedCategory = '全部'.obs;

  KnowledgeBaseScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('知识库管理'),
        actions: [
          // 帮助按钮
          IconButton(
            icon: const Icon(Icons.help_outline),
            tooltip: '知识库使用帮助',
            onPressed: () => _showHelpDialog(context),
          ),
          // 多选模式切换按钮
          Obx(() => IconButton(
                icon: Icon(controller.isMultiSelectMode.value
                    ? Icons.check_circle
                    : Icons.check_circle_outline),
                tooltip:
                    controller.isMultiSelectMode.value ? '退出多选模式' : '进入多选模式',
                onPressed: () => controller.toggleMultiSelectMode(),
              )),
          // 添加知识按钮
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: '添加知识',
            onPressed: () => _showAddDocumentDialog(context),
          ),
        ],
      ),
      body: Obx(() => Column(
            children: [
              // 搜索栏
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextField(
                  controller: searchController,
                  decoration: InputDecoration(
                    hintText: '搜索知识文档...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: searchQuery.value.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              searchController.clear();
                              searchQuery.value = '';
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onChanged: (value) {
                    searchQuery.value = value;
                  },
                ),
              ),

              // 分类标签
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '分类筛选',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8.0,
                      runSpacing: 8.0,
                      children: [
                        // 全部分类选项
                        Obx(() => FilterChip(
                          label: Text('全部 (${controller.documents.length})'),
                          selected: selectedCategory.value == '全部',
                          onSelected: (selected) {
                            selectedCategory.value = '全部';
                          },
                        )),
                        ...controller.categories.map((category) {
                          final count = controller.documents
                              .where((doc) => doc.category == category)
                              .length;
                          return Obx(() => FilterChip(
                            label: Text('$category ($count)'),
                            selected: selectedCategory.value == category,
                            onSelected: (selected) {
                              selectedCategory.value = category;
                            },
                          ));
                        }),
                        ActionChip(
                          avatar: const Icon(Icons.add, size: 18),
                          label: const Text('添加分类'),
                          onPressed: () => _showAddCategoryDialog(context),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // 多选模式下的操作栏
              if (controller.isMultiSelectMode.value)
                Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 16.0, vertical: 8.0),
                  color: Colors.blue.withOpacity(0.1),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('已选择 ${controller.selectedDocIds.length} 个文档'),
                      Row(
                        children: [
                          TextButton.icon(
                            icon: const Icon(Icons.select_all),
                            label: const Text('全选'),
                            onPressed: () => controller.selectAllDocuments(),
                          ),
                          TextButton.icon(
                            icon: const Icon(Icons.deselect),
                            label: const Text('取消全选'),
                            onPressed: () => controller.deselectAllDocuments(),
                          ),
                          TextButton.icon(
                            icon: const Icon(Icons.clear),
                            label: const Text('清除选择'),
                            onPressed: () => controller.clearSelection(),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

              // 文档列表
              Expanded(
                child: Obx(() {
                  final filteredDocs = _getFilteredDocuments();

                  if (filteredDocs.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            searchQuery.value.isNotEmpty || selectedCategory.value != '全部'
                                ? Icons.search_off
                                : Icons.library_books,
                            size: 64,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            searchQuery.value.isNotEmpty || selectedCategory.value != '全部'
                                ? '没有找到匹配的文档'
                                : '暂无知识文档，请添加',
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                          if (searchQuery.value.isNotEmpty || selectedCategory.value != '全部')
                            TextButton(
                              onPressed: () {
                                searchController.clear();
                                searchQuery.value = '';
                                selectedCategory.value = '全部';
                              },
                              child: const Text('清除筛选'),
                            ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    itemCount: filteredDocs.length,
                    itemBuilder: (context, index) {
                      final doc = filteredDocs[index];
                      return _buildDocumentCard(context, doc);
                    },
                  );
                }),
              ),
            ],
          )),
      floatingActionButton: FloatingActionButton(
        child: const Icon(Icons.add),
        onPressed: () => _showAddDocumentDialog(context),
      ),
    );
  }

  // 获取筛选后的文档列表
  List<KnowledgeDocument> _getFilteredDocuments() {
    var docs = controller.documents.toList();

    // 按分类筛选
    if (selectedCategory.value != '全部') {
      docs = docs.where((doc) => doc.category == selectedCategory.value).toList();
    }

    // 按搜索关键词筛选
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      docs = docs.where((doc) {
        return doc.title.toLowerCase().contains(query) ||
               doc.content.toLowerCase().contains(query) ||
               doc.category.toLowerCase().contains(query);
      }).toList();
    }

    // 按更新时间排序，最新的在前
    docs.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

    return docs;
  }

  // 根据分类获取颜色
  Color _getCategoryColor(String category) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];

    final index = category.hashCode % colors.length;
    return colors[index.abs()];
  }

  Widget _buildDocumentCard(BuildContext context, KnowledgeDocument doc) {
    final isSelected = controller.selectedDocIds.contains(doc.id);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      elevation: isSelected ? 4 : 1,
      color: isSelected ? Colors.blue.withOpacity(0.1) : null,
      child: InkWell(
        onTap: controller.isMultiSelectMode.value
            ? () => controller.toggleDocumentSelection(doc.id)
            : () => _showDocumentDetail(context, doc),
        onLongPress: () {
          if (!controller.isMultiSelectMode.value) {
            controller.toggleMultiSelectMode();
            controller.toggleDocumentSelection(doc.id);
          }
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListTile(
              leading: controller.isMultiSelectMode.value
                  ? Checkbox(
                      value: isSelected,
                      onChanged: (bool? value) {
                        controller.toggleDocumentSelection(doc.id);
                      },
                    )
                  : CircleAvatar(
                      backgroundColor: _getCategoryColor(doc.category),
                      child: Text(
                        doc.category.isNotEmpty ? doc.category[0] : '?',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
              title: Text(
                doc.title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isSelected ? Colors.blue : null,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getCategoryColor(doc.category).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          doc.category,
                          style: TextStyle(
                            fontSize: 12,
                            color: _getCategoryColor(doc.category),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '更新: ${doc.updatedAt.toString().substring(0, 16)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    doc.content,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ),
              trailing: !controller.isMultiSelectMode.value
                  ? PopupMenuButton<String>(
                      icon: const Icon(Icons.more_vert),
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            _showEditDocumentDialog(context, doc);
                            break;
                          case 'delete':
                            _confirmDeleteDocument(context, doc);
                            break;
                          case 'select':
                            controller.toggleMultiSelectMode();
                            controller.toggleDocumentSelection(doc.id);
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit),
                              SizedBox(width: 8),
                              Text('编辑'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'select',
                          child: Row(
                            children: [
                              Icon(Icons.check_circle_outline),
                              SizedBox(width: 8),
                              Text('选择'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Text('删除', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                    )
                  : null,
            ),

            // 预览前100个字符的内容
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                doc.content.length > 100
                    ? '${doc.content.substring(0, 100)}...'
                    : doc.content,
                style: TextStyle(
                  color: Colors.grey[700],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示添加分类对话框
  void _showAddCategoryDialog(BuildContext context) {
    final TextEditingController categoryController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加分类'),
        content: TextField(
          controller: categoryController,
          decoration: const InputDecoration(
            labelText: '分类名称',
            hintText: '输入新分类名称',
          ),
        ),
        actions: [
          TextButton(
            child: const Text('取消'),
            onPressed: () => Navigator.of(context).pop(),
          ),
          TextButton(
            child: const Text('添加'),
            onPressed: () {
              if (categoryController.text.isNotEmpty) {
                controller.addCategory(categoryController.text);
                Navigator.of(context).pop();
              }
            },
          ),
        ],
      ),
    );
  }

  // 显示添加文档对话框
  void _showAddDocumentDialog(BuildContext context) {
    final titleController = TextEditingController();
    final contentController = TextEditingController();
    final selectedCategory = controller.categories.first.obs;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加知识'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: '标题',
                  hintText: '知识标题',
                ),
              ),
              const SizedBox(height: 16),
              Obx(() => ThemedDropdownButtonFormField<String>(
                    value: selectedCategory.value,
                    decoration: const InputDecoration(
                      labelText: '分类',
                      hintText: '选择分类',
                    ),
                    items: controller.categories
                        .map((category) => DropdownMenuItem(
                              value: category,
                              child: Text(category),
                            ))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        selectedCategory.value = value;
                      }
                    },
                  )),
              const SizedBox(height: 16),
              TextField(
                controller: contentController,
                maxLines: 8,
                decoration: const InputDecoration(
                  labelText: '内容',
                  hintText: '输入知识内容',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            child: const Text('取消'),
            onPressed: () => Navigator.of(context).pop(),
          ),
          TextButton(
            child: const Text('保存'),
            onPressed: () {
              if (titleController.text.isNotEmpty &&
                  contentController.text.isNotEmpty) {
                controller.addDocument(KnowledgeDocument(
                  title: titleController.text,
                  content: contentController.text,
                  category: selectedCategory.value,
                ));
                Navigator.of(context).pop();
              }
            },
          ),
        ],
      ),
    );
  }

  // 显示编辑文档对话框
  void _showEditDocumentDialog(BuildContext context, KnowledgeDocument doc) {
    final titleController = TextEditingController(text: doc.title);
    final contentController = TextEditingController(text: doc.content);
    final selectedCategory = doc.category.obs;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑知识'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: '标题',
                ),
              ),
              const SizedBox(height: 16),
              Obx(() => ThemedDropdownButtonFormField<String>(
                    value: selectedCategory.value,
                    decoration: const InputDecoration(
                      labelText: '分类',
                    ),
                    items: controller.categories
                        .map((category) => DropdownMenuItem(
                              value: category,
                              child: Text(category),
                            ))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        selectedCategory.value = value;
                      }
                    },
                  )),
              const SizedBox(height: 16),
              TextField(
                controller: contentController,
                maxLines: 10,
                decoration: const InputDecoration(
                  labelText: '内容',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            child: const Text('取消'),
            onPressed: () => Navigator.of(context).pop(),
          ),
          TextButton(
            child: const Text('保存'),
            onPressed: () {
              if (titleController.text.isNotEmpty &&
                  contentController.text.isNotEmpty) {
                final updatedDoc = KnowledgeDocument(
                  id: doc.id,
                  title: titleController.text,
                  content: contentController.text,
                  category: selectedCategory.value,
                  createdAt: doc.createdAt,
                );
                controller.updateDocument(updatedDoc);
                Navigator.of(context).pop();
              }
            },
          ),
        ],
      ),
    );
  }

  // 确认删除文档
  void _confirmDeleteDocument(BuildContext context, KnowledgeDocument doc) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除 "${doc.title}" 吗？此操作不可撤销。'),
        actions: [
          TextButton(
            child: const Text('取消'),
            onPressed: () => Navigator.of(context).pop(),
          ),
          TextButton(
            child: const Text('删除', style: TextStyle(color: Colors.red)),
            onPressed: () {
              controller.deleteDocument(doc.id);
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('已删除：${doc.title}')),
              );
            },
          ),
        ],
      ),
    );
  }

  // 显示文档详情
  void _showDocumentDetail(BuildContext context, KnowledgeDocument doc) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(doc.title),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('分类: ${doc.category}',
                      style: const TextStyle(fontWeight: FontWeight.bold)),
                ],
              ),
              Text('创建: ${doc.createdAt.toString().substring(0, 16)}'),
              Text('更新: ${doc.updatedAt.toString().substring(0, 16)}'),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              SelectableText(doc.content),
            ],
          ),
        ),
        actions: [
          TextButton(
            child: const Text('关闭'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  // 显示帮助对话框
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.help_outline, color: Colors.blue),
              SizedBox(width: 8),
              Text('知识库使用帮助'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHelpSection(
                  '📚 知识库作用',
                  '知识库是AI创作的重要辅助工具，可以为小说生成提供专业知识和创作指导。'
                  '当您启用知识库并选择相关文档后，AI会根据这些内容来指导创作，确保作品的专业性和质量。',
                ),
                const SizedBox(height: 16),
                _buildHelpSection(
                  '🎯 分类说明',
                  '',
                ),
                const SizedBox(height: 8),
                _buildCategoryHelp(
                  '专业知识',
                  '用于存放各领域的专业知识',
                  '心理学理论、医学知识、历史背景、科学原理等',
                  'AI会将这些内容作为参考资料，确保创作的专业性和准确性',
                  Colors.blue,
                ),
                const SizedBox(height: 12),
                _buildCategoryHelp(
                  '写作技巧',
                  '用于存放创作要求和写作方法',
                  '爽文技巧、情节构建、角色塑造、文风要求等',
                  'AI会严格按照这些技巧和要求进行创作，提高作品质量',
                  Colors.green,
                ),
                const SizedBox(height: 12),
                _buildCategoryHelp(
                  '其他分类',
                  '用于存放通用的参考内容',
                  '世界观设定、背景资料、参考素材等',
                  'AI会将这些内容作为一般性参考',
                  Colors.orange,
                ),
                const SizedBox(height: 16),
                _buildHelpSection(
                  '💡 使用建议',
                  '1. 根据内容性质选择合适的分类\n'
                  '2. 专业知识：确保内容准确，AI会谨慎引用\n'
                  '3. 写作技巧：明确具体，AI会严格执行\n'
                  '4. 启用知识库后记得选择相关文档\n'
                  '5. 定期整理和更新知识库内容',
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              child: const Text('我知道了'),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        );
      },
    );
  }

  // 构建帮助章节
  Widget _buildHelpSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        if (content.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black54,
              height: 1.4,
            ),
          ),
        ],
      ],
    );
  }

  // 构建分类帮助卡片
  Widget _buildCategoryHelp(
    String categoryName,
    String description,
    String examples,
    String aiUsage,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(
            width: 4,
            color: color,
          ),
        ),
        color: color.withOpacity(0.05),
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  categoryName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            '示例：$examples',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            'AI处理：$aiUsage',
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
